<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - VAITH</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/admin.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar" id="adminSidebar">
            <div class="sidebar-header">
                <a href="admin-dashboard.html" class="sidebar-logo">VAITH</a>
            </div>
            <nav class="sidebar-nav">
                <div class="nav-item">
                    <a href="admin-dashboard.html" class="nav-link active">
                        <i class="nav-icon fas fa-chart-line"></i>
                        <span class="nav-text">Dashboard</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="admin-users.html" class="nav-link">
                        <i class="nav-icon fas fa-users"></i>
                        <span class="nav-text">Users</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="admin-products.html" class="nav-link">
                        <i class="nav-icon fas fa-box"></i>
                        <span class="nav-text">Products</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="admin-orders.html" class="nav-link">
                        <i class="nav-icon fas fa-shopping-cart"></i>
                        <span class="nav-text">Orders</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="admin-analytics.html" class="nav-link">
                        <i class="nav-icon fas fa-chart-bar"></i>
                        <span class="nav-text">Analytics</span>
                    </a>
                </div>

                <!-- Navigation Divider -->
                <div style="margin: 1.5rem 1rem; border-top: 1px solid var(--admin-border); opacity: 0.5;"></div>

                <!-- Quick Links Section -->
                <div style="padding: 0 1rem; margin-bottom: 1rem;">
                    <div style="font-size: 0.75rem; font-weight: 600; color: var(--admin-text-tertiary); text-transform: uppercase; letter-spacing: 0.05em; margin-bottom: 0.75rem;">
                        Quick Links
                    </div>
                </div>

                <div class="nav-item">
                    <a href="index.html" class="nav-link" target="_blank">
                        <i class="nav-icon fas fa-external-link-alt"></i>
                        <span class="nav-text">View Store</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="user-settings.html" class="nav-link">
                        <i class="nav-icon fas fa-cog"></i>
                        <span class="nav-text">Settings</span>
                    </a>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-main" id="adminMain">
            <!-- Header -->
            <header class="admin-header">
                <div class="admin-header-left">
                    <button class="sidebar-toggle" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="page-title">Dashboard</h1>
                </div>
                <div class="admin-header-right">
                    <button class="nav-icon theme-toggle" id="themeToggle" title="Toggle dark mode">
                        <i class="fas fa-moon" id="themeIcon"></i>
                    </button>
                    <div class="user-menu">
                        <button class="nav-icon" id="userMenuBtn">
                            <i class="fas fa-user-circle"></i>
                        </button>
                        <div class="dropdown-menu" id="userDropdown">
                            <a href="user-profile.html" class="dropdown-item">
                                <i class="fas fa-user"></i> Profile
                            </a>
                            <a href="user-settings.html" class="dropdown-item">
                                <i class="fas fa-cog"></i> Settings
                            </a>
                            <hr class="dropdown-divider">
                            <a href="#" class="dropdown-item" id="logoutBtn">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Content -->
            <div class="admin-content">
                <!-- Breadcrumb Navigation -->
                <nav class="breadcrumb fade-in">
                    <div class="breadcrumb-item">
                        <a href="admin-dashboard.html" class="breadcrumb-link">
                            <i class="fas fa-home"></i> Admin
                        </a>
                    </div>
                    <div class="breadcrumb-item">
                        <span class="breadcrumb-current">Dashboard</span>
                    </div>
                </nav>

                <!-- Welcome Section -->
                <div class="page-header fade-in">
                    <h2 class="page-title">Welcome back, <span id="adminName">Admin</span>! 👋</h2>
                    <p class="page-subtitle">Here's what's happening with your store today. Monitor your key metrics and recent activity.</p>
                </div>

                <!-- Dashboard Cards -->
                <div class="dashboard-grid">
                    <div class="dashboard-card fade-in" style="animation-delay: 0.1s;">
                        <div class="card-header">
                            <div class="card-icon users">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="card-content">
                                <h3 id="totalUsers">
                                    <span class="loading-spinner" id="usersLoader"></span>
                                    <span id="usersCount" style="display: none;">0</span>
                                </h3>
                                <p>Total Users</p>
                                <div class="card-trend trend-up" id="usersTrendContainer" style="display: none;">
                                    <i class="fas fa-arrow-up"></i>
                                    <span id="usersTrend">+12%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-card fade-in" style="animation-delay: 0.2s;">
                        <div class="card-header">
                            <div class="card-icon orders">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <div class="card-content">
                                <h3 id="totalOrders">
                                    <span class="loading-spinner" id="ordersLoader"></span>
                                    <span id="ordersCount" style="display: none;">0</span>
                                </h3>
                                <p>Total Orders</p>
                                <div class="card-trend trend-up" id="ordersTrendContainer" style="display: none;">
                                    <i class="fas fa-arrow-up"></i>
                                    <span id="ordersTrend">+8%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-card fade-in" style="animation-delay: 0.3s;">
                        <div class="card-header">
                            <div class="card-icon revenue">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="card-content">
                                <h3 id="totalRevenue">
                                    <span class="loading-spinner" id="revenueLoader"></span>
                                    <span id="revenueAmount" style="display: none;">$0</span>
                                </h3>
                                <p>Total Revenue</p>
                                <div class="card-trend trend-up" id="revenueTrendContainer" style="display: none;">
                                    <i class="fas fa-arrow-up"></i>
                                    <span id="revenueTrend">+15%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-card fade-in" style="animation-delay: 0.4s;">
                        <div class="card-header">
                            <div class="card-icon products">
                                <i class="fas fa-box"></i>
                            </div>
                            <div class="card-content">
                                <h3 id="totalProducts">
                                    <span class="loading-spinner" id="productsLoader"></span>
                                    <span id="productsCount" style="display: none;">0</span>
                                </h3>
                                <p>Total Products</p>
                                <div class="card-trend trend-up" id="productsTrendContainer" style="display: none;">
                                    <i class="fas fa-arrow-up"></i>
                                    <span id="productsTrend">+5%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="data-table-container fade-in" style="animation-delay: 0.5s;">
                    <div class="table-header">
                        <h3 class="table-title">📊 Recent Users</h3>
                        <div class="table-actions">
                            <a href="admin-users.html" class="btn btn-primary btn-sm" title="View All Users">
                                <i class="fas fa-eye"></i> View All Users
                            </a>
                            <button class="btn btn-secondary btn-sm" onclick="refreshRecentUsers()" title="Refresh Data">
                                <i class="fas fa-sync-alt"></i> Refresh
                            </button>
                        </div>
                    </div>
                    <div id="recentUsersLoading" class="loading-skeleton" style="height: 300px; margin: 20px;"></div>
                    <table class="data-table" id="recentUsersTableContainer" style="display: none;">
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Email</th>
                                <th>Status</th>
                                <th>Join Date</th>
                                <th>Last Login</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="recentUsersTable">
                            <!-- Users will be loaded dynamically -->
                        </tbody>
                    </table>
                </div>

                <!-- Quick Actions Section -->
                <div class="dashboard-grid" style="margin-top: 2rem; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));">
                    <div class="dashboard-card fade-in" style="animation-delay: 0.6s;">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #8b5cf6 0%, #a78bfa 100%);">
                                <i class="fas fa-plus"></i>
                            </div>
                            <div class="card-content">
                                <h4 style="margin: 0; color: var(--admin-text-primary);">Quick Actions</h4>
                                <p style="margin: 0.5rem 0; color: var(--admin-text-secondary); font-size: 0.875rem;">Manage your store efficiently</p>
                                <div style="display: flex; flex-direction: column; gap: 0.5rem; margin-top: 1rem;">
                                    <a href="admin-products.html" class="btn btn-secondary btn-sm">
                                        <i class="fas fa-box"></i> Add Product
                                    </a>
                                    <a href="admin-users.html" class="btn btn-secondary btn-sm">
                                        <i class="fas fa-user-plus"></i> Manage Users
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-card fade-in" style="animation-delay: 0.7s;">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="card-content">
                                <h4 style="margin: 0; color: var(--admin-text-primary);">Analytics</h4>
                                <p style="margin: 0.5rem 0; color: var(--admin-text-secondary); font-size: 0.875rem;">View detailed reports</p>
                                <div style="display: flex; flex-direction: column; gap: 0.5rem; margin-top: 1rem;">
                                    <a href="admin-analytics.html" class="btn btn-secondary btn-sm">
                                        <i class="fas fa-chart-bar"></i> View Reports
                                    </a>
                                    <a href="admin-orders.html" class="btn btn-secondary btn-sm">
                                        <i class="fas fa-shopping-cart"></i> Order History
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- User Menu Dropdown Styles -->
    <style>
        .user-menu {
            position: relative;
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            min-width: 200px;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
        }

        .dropdown-menu.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1rem;
            color: var(--text-color);
            text-decoration: none;
            transition: background-color 0.2s;
        }

        .dropdown-item:hover {
            background: var(--section-bg);
        }

        .dropdown-divider {
            margin: 0.5rem 0;
            border: none;
            border-top: 1px solid var(--border-color);
        }
    </style>

    <!-- Scripts -->
    <script src="js/theme-toggle.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/admin.js"></script>

    <script>
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            // Check admin access
            if (!authManager.requireAdmin()) {
                return;
            }

            // Initialize dashboard
            initializeDashboard();
            
            // Setup event listeners
            setupEventListeners();
        });

        function initializeDashboard() {
            const currentUser = authManager.getCurrentUser();
            document.getElementById('adminName').textContent = currentUser.firstName;

            // Load dashboard stats
            loadDashboardStats();
            loadRecentUsers();
        }

        function loadDashboardStats() {
            // Simulate loading delay for better UX
            setTimeout(() => {
                const userStats = authManager.getUserStats();

                // Users
                document.getElementById('usersLoader').style.display = 'none';
                document.getElementById('usersCount').style.display = 'inline';
                document.getElementById('usersCount').textContent = userStats.total;
                document.getElementById('usersTrendContainer').style.display = 'flex';

                // Orders
                document.getElementById('ordersLoader').style.display = 'none';
                document.getElementById('ordersCount').style.display = 'inline';
                document.getElementById('ordersCount').textContent = '156'; // Mock data
                document.getElementById('ordersTrendContainer').style.display = 'flex';

                // Revenue
                document.getElementById('revenueLoader').style.display = 'none';
                document.getElementById('revenueAmount').style.display = 'inline';
                document.getElementById('revenueAmount').textContent = '$12,450';
                document.getElementById('revenueTrendContainer').style.display = 'flex';

                // Products
                document.getElementById('productsLoader').style.display = 'none';
                document.getElementById('productsCount').style.display = 'inline';
                document.getElementById('productsCount').textContent = '89';
                document.getElementById('productsTrendContainer').style.display = 'flex';
            }, 800);
        }

        function loadRecentUsers() {
            // Show loading state
            document.getElementById('recentUsersLoading').style.display = 'block';
            document.getElementById('recentUsersTableContainer').style.display = 'none';

            // Simulate loading delay
            setTimeout(() => {
                const users = authManager.getAllUsers()
                    .sort((a, b) => new Date(b.joinDate) - new Date(a.joinDate))
                    .slice(0, 5);

                const tbody = document.getElementById('recentUsersTable');
                tbody.innerHTML = users.map(user => `
                    <tr class="slide-in">
                        <td>
                            <div style="display: flex; align-items: center; gap: 0.75rem;">
                                <div class="profile-avatar" style="width: 40px; height: 40px; font-size: 0.875rem; background: linear-gradient(135deg, var(--admin-primary) 0%, var(--admin-primary-hover) 100%); color: white; display: flex; align-items: center; justify-content: center; border-radius: 50%; font-weight: 600;">
                                    ${getInitials(user.firstName, user.lastName)}
                                </div>
                                <div>
                                    <div style="cursor: pointer; color: var(--admin-primary); font-weight: 600; font-size: 0.95rem;"
                                          onclick="viewUserDetails(${user.id})"
                                          title="View user profile">
                                        ${user.firstName} ${user.lastName}
                                    </div>
                                    <div style="font-size: 0.75rem; color: var(--admin-text-tertiary);">
                                        ID: ${user.id}
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td style="color: var(--admin-text-secondary);">${user.email}</td>
                        <td><span class="status-badge ${getStatusBadgeClass(user.status)}">${user.status}</span></td>
                        <td style="color: var(--admin-text-secondary);">${formatDate(user.joinDate)}</td>
                        <td style="color: var(--admin-text-secondary);">${user.lastLogin ? formatDateTime(user.lastLogin) : 'Never'}</td>
                        <td>
                            <div style="display: flex; gap: 0.5rem;">
                                <button class="btn btn-secondary btn-sm" onclick="viewUserDetails(${user.id})" title="View User Profile">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <a href="admin-users.html" class="btn btn-secondary btn-sm" title="Manage User">
                                    <i class="fas fa-cog"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                `).join('');

                // Hide loading and show table
                document.getElementById('recentUsersLoading').style.display = 'none';
                document.getElementById('recentUsersTableContainer').style.display = 'table';
            }, 1000);
        }

        function refreshRecentUsers() {
            showToast('Refreshing user data...', 'info');
            loadRecentUsers();
        }

        // Function to view user details - redirects directly to user profile
        function viewUserDetails(userId) {
            // Store the user ID to view their profile
            sessionStorage.setItem('viewUserId', userId);
            // Redirect directly to user profile page
            window.location.href = 'user-profile.html';
        }

        function setupEventListeners() {
            // Sidebar toggle
            document.getElementById('sidebarToggle').addEventListener('click', function() {
                const sidebar = document.getElementById('adminSidebar');
                const main = document.getElementById('adminMain');
                
                sidebar.classList.toggle('collapsed');
                main.classList.toggle('expanded');
            });

            // User menu toggle
            document.getElementById('userMenuBtn').addEventListener('click', function(e) {
                e.stopPropagation();
                const dropdown = document.getElementById('userDropdown');
                dropdown.classList.toggle('show');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function() {
                const dropdown = document.getElementById('userDropdown');
                dropdown.classList.remove('show');
            });

            // Logout
            document.getElementById('logoutBtn').addEventListener('click', function(e) {
                e.preventDefault();
                showToast('Logging out...', 'info');
                setTimeout(() => {
                    authManager.logout();
                }, 1000);
            });
        }

        // Modern Toast Notification System
        function showToast(message, type = 'info', duration = 3000) {
            const toastContainer = getOrCreateToastContainer();

            const toast = document.createElement('div');
            toast.className = `toast ${type}`;

            toast.innerHTML = `
                <div class="toast-header">
                    <div class="toast-title">
                        ${type === 'success' ? '✅ Success' :
                          type === 'error' ? '❌ Error' :
                          type === 'warning' ? '⚠️ Warning' :
                          '💡 Info'}
                    </div>
                    <button class="toast-close" onclick="removeToast(this.parentElement.parentElement)">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="toast-message">${message}</div>
            `;

            toastContainer.appendChild(toast);

            // Auto remove after duration
            setTimeout(() => {
                removeToast(toast);
            }, duration);
        }

        function getOrCreateToastContainer() {
            let container = document.querySelector('.toast-container');
            if (!container) {
                container = document.createElement('div');
                container.className = 'toast-container';
                document.body.appendChild(container);
            }
            return container;
        }

        function removeToast(toast) {
            if (toast && toast.parentElement) {
                toast.style.animation = 'slideOutRight 0.3s ease-in forwards';
                setTimeout(() => {
                    if (toast.parentElement) {
                        toast.parentElement.removeChild(toast);
                    }
                }, 300);
            }
        }

        // Add slideOutRight animation to CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideOutRight {
                from {
                    opacity: 1;
                    transform: translateX(0);
                }
                to {
                    opacity: 0;
                    transform: translateX(100%);
                }
            }
        `;
        document.head.appendChild(style);

        // Welcome message on load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                showToast('Welcome to your modern admin dashboard! 🎉', 'success', 4000);
            }, 2000);
        });
    </script>
</body>
</html>
